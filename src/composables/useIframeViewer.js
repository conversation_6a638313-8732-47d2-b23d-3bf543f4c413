import { ref } from 'vue';

/**
 * iframe 查看器 composable
 * 统一管理 iframe 的显示、隐藏和事件处理逻辑
 */
export function useIframeViewer() {
    // iframe 相关状态
    const showIframe = ref(false);
    const currentCourseUrl = ref('');
    const currentCourseTitle = ref('');

    /**
     * 打开 iframe 查看器
     * @param {Object} options - 配置选项
     * @param {string} options.url - 要显示的 URL
     * @param {string} options.title - iframe 标题
     */
    const openIframe = ({ url, title = '' }) => {
        // 验证 URL 是否存在
        if (!url) {
            console.warn('缺少目标链接');
            return false;
        }

        try {
            // 验证 URL 格式
            new URL(url);

            // 设置 iframe 相关数据并显示
            currentCourseUrl.value = url;
            currentCourseTitle.value = title;
            showIframe.value = true;
            
            return true;
        } catch (error) {
            console.error('无效的链接:', url, error);
            return false;
        }
    };

    /**
     * 关闭 iframe 查看器
     */
    const closeIframe = () => {
        showIframe.value = false;
        currentCourseUrl.value = '';
        currentCourseTitle.value = '';
    };

    /**
     * 处理 iframe 返回事件
     */
    const handleIframeBack = () => {
        closeIframe();
    };

    /**
     * 处理 iframe 加载完成事件
     */
    const handleIframeLoad = () => {
        console.log('iframe 加载完成');
    };

    /**
     * 处理 iframe 加载错误事件
     */
    const handleIframeError = (error) => {
        console.error('iframe 加载错误:', error);
        // 可以在这里添加错误处理逻辑，比如显示错误提示
    };

    /**
     * 处理课程点击事件，使用 iframe 打开课程链接
     * @param {Object} course - 课程对象
     * @param {string} course.targetUrl - 课程链接
     * @param {string} course.name - 课程名称
     */
    const handleCourseClick = (course) => {
        if (!course.targetUrl) {
            console.warn('课程缺少目标链接:', course.name);
            return;
        }

        const success = openIframe({
            url: course.targetUrl,
            title: course.name
        });

        if (success) {
            console.log('打开课程:', course.name);
        }
    };

    return {
        // 状态
        showIframe,
        currentCourseUrl,
        currentCourseTitle,
        
        // 方法
        openIframe,
        closeIframe,
        handleIframeBack,
        handleIframeLoad,
        handleIframeError,
        handleCourseClick
    };
}
