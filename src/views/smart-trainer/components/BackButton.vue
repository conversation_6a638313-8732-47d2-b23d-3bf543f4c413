<template>
    <div class="back-button" @click="handleBack">
        <i class="pi pi-arrow-left"></i>
        <span v-if="title">{{ title }}</span>
    </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { computed } from 'vue';

const props = defineProps({
    /**
     * 返回按钮标题
     */
    title: {
        type: String,
        default: ''
    },
    /**
     * 自定义返回路径
     */
    backPath: {
        type: String,
        default: ''
    },
    /**
     * 智能返回模式：自动根据当前路径推断返回路径
     */
    smartBack: {
        type: Boolean,
        default: false
    }
});

const router = useRouter();
const route = useRoute();

/**
 * 智能推断返回路径
 */
const smartBackPath = computed(() => {
    const currentPath = route.path;

    // 考试相关页面的返回逻辑
    if (currentPath.includes('/assess/detail/')) {
        return '/smart-trainer/assess/home';
    }
    if (currentPath.includes('/assess/quiz/')) {
        return '/smart-trainer/assess/home';
    }
    if (currentPath.includes('/assess/result/')) {
        return '/smart-trainer/assess/home';
    }

    // 练习相关页面的返回逻辑
    if (currentPath.includes('/practice/') && !currentPath.endsWith('/practice')) {
        return '/smart-trainer/practice';
    }

    // 学习相关页面的返回逻辑
    if (currentPath.includes('/learn/') && !currentPath.endsWith('/learn/home')) {
        return '/smart-trainer/learn/home';
    }

    // 默认返回主页
    return '/smart-trainer/main';
});

/**
 * 处理返回按钮点击
 */
const handleBack = () => {
    if (props.backPath) {
        // 优先使用自定义返回路径
        router.push(props.backPath);
    } else if (props.smartBack) {
        // 使用智能返回路径
        router.push(smartBackPath.value);
    } else {
        // 使用浏览器返回
        router.back();
    }
};
</script>

<style lang="scss" scoped>
.back-button {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;

    i {
        margin-right: 8px;
        font-size: 18px;
    }
}
</style>
