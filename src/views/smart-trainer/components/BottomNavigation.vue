<template>
    <nav class="bottom-navigation">
        <div class="nav-container">
            <RouterLink
                v-for="item in navigationItems"
                :key="item.key"
                :to="item.path"
                custom
                v-slot="{ isActive, navigate }"
            >
                <div
                    :class="['nav-item', { active: isActive }]"
                    @click="handleNavClick(item, navigate)"
                >
                    <div class="nav-icon">
                        <i :class="item.icon"></i>
                    </div>
                    <span class="nav-label">{{ item.label }}</span>
                </div>
            </RouterLink>
        </div>
    </nav>
</template>

<script setup>
import { ref } from 'vue';

// 导航项配置
const navigationItems = ref([
    {
        key: 'main',
        label: '首页',
        icon: 'pi pi-home',
        path: '/smart-trainer/main'
    },
    {
        key: 'learn',
        label: '课程',
        icon: 'pi pi-book',
        path: '/smart-trainer/learn/home'
    },
    {
        key: 'practice',
        label: '练习',
        icon: 'pi pi-play-circle',
        path: '/smart-trainer/practice'
    },
    {
        key: 'assess',
        label: '考试',
        icon: 'pi pi-file-edit',
        path: '/smart-trainer/assess/home'
    },
    {
        key: 'profile',
        label: '我的',
        icon: 'pi pi-user',
        path: '/smart-trainer/profile'
    }
]);

/**
 * 处理导航点击
 */
const handleNavClick = (item, navigate) => {
    console.log(`导航到: ${item.label} (${item.path})`);
    navigate();
};
</script>

<style lang="scss" scoped>
.bottom-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .nav-container {
        display: flex;
        height: 60px;
        max-width: 100%;
        margin: 0 auto;

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px 4px;

            .nav-icon {
                position: relative;
                margin-bottom: 4px;

                i {
                    font-size: 16px;
                    color: #6b7280;
                    transition: color 0.3s ease;
                }
            }

            .nav-label {
                font-size: 14px;
                color: #6b7280;
                font-weight: 500;
                transition: color 0.3s ease;
                text-align: center;
                line-height: 1;
            }

            &.active {
                .nav-icon i {
                    color: #3b82f6;
                }

                .nav-label {
                    color: #3b82f6;
                    font-weight: 600;
                }
            }

            &:hover:not(.active) {
                .nav-icon i {
                    color: #4b5563;
                }

                .nav-label {
                    color: #4b5563;
                }
            }
        }
    }
}

// 适配安全区域（iPhone X 等设备）
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .bottom-navigation {
        padding-bottom: env(safe-area-inset-bottom);
    }
}
</style>
